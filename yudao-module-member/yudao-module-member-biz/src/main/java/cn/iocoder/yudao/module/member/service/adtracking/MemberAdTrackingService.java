package cn.iocoder.yudao.module.member.service.adtracking;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingCreateReqVO;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingPageReqVO;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingUpdateConversionReqVO;
import cn.iocoder.yudao.module.member.dal.dataobject.adtracking.MemberAdTrackingDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员广告跟踪 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberAdTrackingService {

    /**
     * 创建广告跟踪记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAdTracking(@Valid AppMemberAdTrackingCreateReqVO createReqVO);

    /**
     * 更新转化信息
     *
     * @param updateReqVO 更新信息
     */
    void updateConversion(@Valid AppMemberAdTrackingUpdateConversionReqVO updateReqVO);

    /**
     * 根据用户ID获取广告跟踪记录
     *
     * @param userId 用户ID
     * @return 广告跟踪记录列表
     */
    List<MemberAdTrackingDO> getAdTrackingListByUserId(Long userId);

    /**
     * 根据会话ID获取广告跟踪记录
     *
     * @param sessionId 会话ID
     * @return 广告跟踪记录
     */
    MemberAdTrackingDO getAdTrackingBySessionId(String sessionId);

    /**
     * 获得广告跟踪记录
     *
     * @param id 编号
     * @return 广告跟踪记录
     */
    MemberAdTrackingDO getAdTracking(Long id);

    /**
     * 获得广告跟踪记录分页
     *
     * @param pageReqVO 分页查询
     * @return 广告跟踪记录分页
     */
    PageResult<MemberAdTrackingDO> getAdTrackingPage(AppMemberAdTrackingPageReqVO pageReqVO);

    /**
     * 记录注册转化
     *
     * @param userId 用户ID
     * @param sessionId 会话ID（可选）
     */
    void recordRegisterConversion(Long userId, String sessionId);

    /**
     * 记录订单转化
     *
     * @param userId 用户ID
     * @param orderValue 订单价值
     */
    void recordOrderConversion(Long userId, java.math.BigDecimal orderValue);

}
