package cn.iocoder.yudao.module.member.service.adtracking;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingCreateReqVO;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingPageReqVO;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingUpdateConversionReqVO;
import cn.iocoder.yudao.module.member.dal.dataobject.adtracking.MemberAdTrackingDO;
import cn.iocoder.yudao.module.member.dal.mysql.adtracking.MemberAdTrackingMapper;
import cn.iocoder.yudao.module.member.enums.adtracking.AdTrackingConversionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.AD_TRACKING_NOT_EXISTS;

/**
 * 会员广告跟踪 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MemberAdTrackingServiceImpl implements MemberAdTrackingService {

    @Resource
    private MemberAdTrackingMapper adTrackingMapper;

    @Override
    public Long createAdTracking(AppMemberAdTrackingCreateReqVO createReqVO) {
        // 插入
        MemberAdTrackingDO adTracking = BeanUtils.toBean(createReqVO, MemberAdTrackingDO.class);
        adTracking.setIsConverted(false); // 初始状态为未转化
        adTrackingMapper.insert(adTracking);
        // 返回
        return adTracking.getId();
    }

    @Override
    public void updateConversion(AppMemberAdTrackingUpdateConversionReqVO updateReqVO) {
        // 校验存在
        validateAdTrackingExists(updateReqVO.getId());
        // 更新
        MemberAdTrackingDO updateObj = BeanUtils.toBean(updateReqVO, MemberAdTrackingDO.class);
        updateObj.setIsConverted(true);
        updateObj.setConversionTime(LocalDateTime.now());
        adTrackingMapper.updateById(updateObj);
    }

    private void validateAdTrackingExists(Long id) {
        if (adTrackingMapper.selectById(id) == null) {
            throw exception(AD_TRACKING_NOT_EXISTS);
        }
    }

    @Override
    public MemberAdTrackingDO getAdTracking(Long id) {
        return adTrackingMapper.selectById(id);
    }

    @Override
    public List<MemberAdTrackingDO> getAdTrackingListByUserId(Long userId) {
        return adTrackingMapper.selectListByUserId(userId);
    }

    @Override
    public MemberAdTrackingDO getAdTrackingBySessionId(String sessionId) {
        return adTrackingMapper.selectBySessionId(sessionId);
    }

    @Override
    public PageResult<MemberAdTrackingDO> getAdTrackingPage(AppMemberAdTrackingPageReqVO pageReqVO) {
        return adTrackingMapper.selectPage(pageReqVO, pageReqVO.getUserId(), pageReqVO.getUtmSource(),
                pageReqVO.getUtmCampaign(), pageReqVO.getIsConverted(), 
                pageReqVO.getBeginTime(), pageReqVO.getEndTime());
    }

    @Override
    public void recordRegisterConversion(Long userId, String sessionId) {
        // 查找用户的广告跟踪记录
        List<MemberAdTrackingDO> trackingList = adTrackingMapper.selectListByUserIdAndConverted(userId, false);
        
        // 如果有会话ID，优先匹配会话ID
        MemberAdTrackingDO targetTracking = null;
        if (sessionId != null) {
            targetTracking = trackingList.stream()
                    .filter(tracking -> sessionId.equals(tracking.getSessionId()))
                    .findFirst()
                    .orElse(null);
        }
        
        // 如果没有找到匹配的会话，取最新的一条记录
        if (targetTracking == null && !trackingList.isEmpty()) {
            targetTracking = trackingList.get(0); // 已按ID倒序排列
        }
        
        // 更新转化信息
        if (targetTracking != null) {
            MemberAdTrackingDO updateObj = new MemberAdTrackingDO();
            updateObj.setId(targetTracking.getId());
            updateObj.setIsConverted(true);
            updateObj.setConversionType(AdTrackingConversionTypeEnum.REGISTER.getType());
            updateObj.setConversionTime(LocalDateTime.now());
            adTrackingMapper.updateById(updateObj);
            
            log.info("[recordRegisterConversion][用户({})注册转化成功，广告跟踪ID({})]", userId, targetTracking.getId());
        }
    }

    @Override
    public void recordOrderConversion(Long userId, BigDecimal orderValue) {
        // 查找用户最新的广告跟踪记录
        List<MemberAdTrackingDO> trackingList = adTrackingMapper.selectListByUserId(userId);
        if (trackingList.isEmpty()) {
            return;
        }
        
        MemberAdTrackingDO latestTracking = trackingList.get(0); // 已按ID倒序排列
        
        // 更新转化信息
        MemberAdTrackingDO updateObj = new MemberAdTrackingDO();
        updateObj.setId(latestTracking.getId());
        updateObj.setIsConverted(true);
        updateObj.setConversionType(AdTrackingConversionTypeEnum.ORDER.getType());
        updateObj.setConversionValue(orderValue);
        updateObj.setConversionTime(LocalDateTime.now());
        adTrackingMapper.updateById(updateObj);
        
        log.info("[recordOrderConversion][用户({})订单转化成功，订单价值({})，广告跟踪ID({})]", 
                userId, orderValue, latestTracking.getId());
    }

}
