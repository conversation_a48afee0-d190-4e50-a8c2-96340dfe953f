package cn.iocoder.yudao.module.member.controller.app.adtracking.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "用户 APP - 会员广告跟踪更新转化 Request VO")
@Data
public class AppMemberAdTrackingUpdateConversionReqVO {

    @Schema(description = "广告跟踪ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "广告跟踪ID不能为空")
    private Long id;

    @Schema(description = "转化类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDER")
    @NotNull(message = "转化类型不能为空")
    private String conversionType;

    @Schema(description = "转化价值", example = "99.99")
    private BigDecimal conversionValue;

}
