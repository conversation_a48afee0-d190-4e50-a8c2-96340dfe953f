package cn.iocoder.yudao.module.member.controller.app.adtracking.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户 APP - 会员广告跟踪分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppMemberAdTrackingPageReqVO extends PageParam {

    @Schema(description = "用户ID", example = "1024")
    private Long userId;

    @Schema(description = "广告来源", example = "google")
    private String utmSource;

    @Schema(description = "广告活动", example = "summer_sale")
    private String utmCampaign;

    @Schema(description = "是否已转化", example = "true")
    private Boolean isConverted;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "开始时间", hidden = true)
    public LocalDateTime getBeginTime() {
        return createTime != null && createTime.length >= 1 ? createTime[0] : null;
    }

    @Schema(description = "结束时间", hidden = true)
    public LocalDateTime getEndTime() {
        return createTime != null && createTime.length >= 2 ? createTime[1] : null;
    }

}
