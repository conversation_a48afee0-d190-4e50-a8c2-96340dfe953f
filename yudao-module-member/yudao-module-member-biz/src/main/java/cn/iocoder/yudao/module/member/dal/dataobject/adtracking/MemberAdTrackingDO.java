package cn.iocoder.yudao.module.member.dal.dataobject.adtracking;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员广告跟踪 DO
 *
 * <AUTHOR>
 */
@TableName(value = "member_ad_tracking")
@KeySequence("member_ad_tracking_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberAdTrackingDO extends TenantBaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话ID
     */
    private String sessionId;

    // ========== UTM参数 ==========

    /**
     * 广告来源
     */
    private String utmSource;

    /**
     * 广告媒介
     */
    private String utmMedium;

    /**
     * 广告活动
     */
    private String utmCampaign;

    /**
     * 广告关键词
     */
    private String utmTerm;

    /**
     * 广告内容
     */
    private String utmContent;

    // ========== 平台特定参数 ==========

    /**
     * Google点击ID
     */
    private String gclid;

    /**
     * Facebook点击ID
     */
    private String fbclid;

    // ========== 跟踪信息 ==========

    /**
     * 着陆页面
     */
    private String landingPage;

    /**
     * 来源页面
     */
    private String referrer;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * IP地址
     */
    private String ipAddress;

    // ========== 转化信息 ==========

    /**
     * 是否已转化
     */
    private Boolean isConverted;

    /**
     * 转化类型
     * 
     * 枚举 {@link cn.iocoder.yudao.module.member.enums.adtracking.AdTrackingConversionTypeEnum}
     */
    private String conversionType;

    /**
     * 转化价值
     */
    private BigDecimal conversionValue;

    /**
     * 转化时间
     */
    private LocalDateTime conversionTime;

}
