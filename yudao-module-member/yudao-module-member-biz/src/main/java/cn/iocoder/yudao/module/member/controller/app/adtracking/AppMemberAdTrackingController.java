package cn.iocoder.yudao.module.member.controller.app.adtracking;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingCreateReqVO;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingPageReqVO;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingRespVO;
import cn.iocoder.yudao.module.member.controller.app.adtracking.vo.AppMemberAdTrackingUpdateConversionReqVO;
import cn.iocoder.yudao.module.member.convert.adtracking.MemberAdTrackingConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.adtracking.MemberAdTrackingDO;
import cn.iocoder.yudao.module.member.service.adtracking.MemberAdTrackingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getClientIP;

/**
 * 用户 APP - 会员广告跟踪 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 会员广告跟踪")
@RestController
@RequestMapping("/member/ad-tracking")
@Validated
@Slf4j
public class AppMemberAdTrackingController {

    @Resource
    private MemberAdTrackingService adTrackingService;

    @PostMapping("/create")
    @Operation(summary = "创建广告跟踪记录")
    public CommonResult<Long> createAdTracking(@Valid @RequestBody AppMemberAdTrackingCreateReqVO createReqVO,
                                               HttpServletRequest request) {
        // 自动设置IP地址
        createReqVO.setIpAddress(getClientIP(request));
        
        Long id = adTrackingService.createAdTracking(createReqVO);
        return success(id);
    }

    @PutMapping("/update-conversion")
    @Operation(summary = "更新转化信息")
    @PreAuthenticated
    public CommonResult<Boolean> updateConversion(@Valid @RequestBody AppMemberAdTrackingUpdateConversionReqVO updateReqVO) {
        adTrackingService.updateConversion(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得广告跟踪记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<AppMemberAdTrackingRespVO> getAdTracking(@RequestParam("id") Long id) {
        MemberAdTrackingDO adTracking = adTrackingService.getAdTracking(id);
        return success(MemberAdTrackingConvert.INSTANCE.convert(adTracking));
    }

    @GetMapping("/list-by-user")
    @Operation(summary = "获取当前用户的广告跟踪记录列表")
    @PreAuthenticated
    public CommonResult<List<AppMemberAdTrackingRespVO>> getAdTrackingListByUser() {
        Long userId = getLoginUserId();
        List<MemberAdTrackingDO> list = adTrackingService.getAdTrackingListByUserId(userId);
        return success(MemberAdTrackingConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/get-by-session")
    @Operation(summary = "根据会话ID获取广告跟踪记录")
    @Parameter(name = "sessionId", description = "会话ID", required = true, example = "abc123")
    public CommonResult<AppMemberAdTrackingRespVO> getAdTrackingBySession(@RequestParam("sessionId") String sessionId) {
        MemberAdTrackingDO adTracking = adTrackingService.getAdTrackingBySessionId(sessionId);
        return success(MemberAdTrackingConvert.INSTANCE.convert(adTracking));
    }

    @GetMapping("/page")
    @Operation(summary = "获得广告跟踪记录分页")
    @PreAuthenticated
    public CommonResult<PageResult<AppMemberAdTrackingRespVO>> getAdTrackingPage(@Valid AppMemberAdTrackingPageReqVO pageReqVO) {
        // 只能查询自己的记录
        pageReqVO.setUserId(getLoginUserId());
        
        PageResult<MemberAdTrackingDO> pageResult = adTrackingService.getAdTrackingPage(pageReqVO);
        return success(MemberAdTrackingConvert.INSTANCE.convertPage(pageResult));
    }

    @PostMapping("/record-register-conversion")
    @Operation(summary = "记录注册转化")
    @PreAuthenticated
    public CommonResult<Boolean> recordRegisterConversion(@RequestParam(value = "sessionId", required = false) String sessionId) {
        Long userId = getLoginUserId();
        adTrackingService.recordRegisterConversion(userId, sessionId);
        return success(true);
    }

}
