package cn.iocoder.yudao.module.member.controller.app.adtracking.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "用户 APP - 会员广告跟踪 Response VO")
@Data
public class AppMemberAdTrackingRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long userId;

    @Schema(description = "会话ID", example = "abc123")
    private String sessionId;

    // ========== UTM参数 ==========

    @Schema(description = "广告来源", example = "google")
    private String utmSource;

    @Schema(description = "广告媒介", example = "cpc")
    private String utmMedium;

    @Schema(description = "广告活动", example = "summer_sale")
    private String utmCampaign;

    @Schema(description = "广告关键词", example = "代购")
    private String utmTerm;

    @Schema(description = "广告内容", example = "banner_top")
    private String utmContent;

    // ========== 平台特定参数 ==========

    @Schema(description = "Google点击ID", example = "Cj0KCQjw...")
    private String gclid;

    @Schema(description = "Facebook点击ID", example = "IwAR0...")
    private String fbclid;

    // ========== 跟踪信息 ==========

    @Schema(description = "着陆页面", example = "https://example.com/products")
    private String landingPage;

    @Schema(description = "来源页面", example = "https://google.com")
    private String referrer;

    @Schema(description = "用户代理", example = "Mozilla/5.0...")
    private String userAgent;

    @Schema(description = "IP地址", example = "***********")
    private String ipAddress;

    // ========== 转化信息 ==========

    @Schema(description = "是否已转化", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean isConverted;

    @Schema(description = "转化类型", example = "ORDER")
    private String conversionType;

    @Schema(description = "转化价值", example = "99.99")
    private BigDecimal conversionValue;

    @Schema(description = "转化时间")
    private LocalDateTime conversionTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
