package cn.iocoder.yudao.module.member.enums.adtracking;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 广告跟踪转化类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum AdTrackingConversionTypeEnum {

    REGISTER("REGISTER", "注册转化"),
    ORDER("ORDER", "订单转化"),
    CUSTOM("CUSTOM", "自定义转化");

    /**
     * 类型
     */
    private final String type;
    /**
     * 名字
     */
    private final String name;

}
