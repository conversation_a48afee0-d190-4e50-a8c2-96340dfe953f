package cn.iocoder.yudao.module.member.dal.mysql.adtracking;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.member.dal.dataobject.adtracking.MemberAdTrackingDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员广告跟踪 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberAdTrackingMapper extends BaseMapperX<MemberAdTrackingDO> {

    /**
     * 根据用户ID查询广告跟踪记录
     *
     * @param userId 用户ID
     * @return 广告跟踪记录列表
     */
    default List<MemberAdTrackingDO> selectListByUserId(Long userId) {
        return selectList(MemberAdTrackingDO::getUserId, userId);
    }

    /**
     * 根据会话ID查询广告跟踪记录
     *
     * @param sessionId 会话ID
     * @return 广告跟踪记录
     */
    default MemberAdTrackingDO selectBySessionId(String sessionId) {
        return selectOne(MemberAdTrackingDO::getSessionId, sessionId);
    }

    /**
     * 根据用户ID和转化状态查询记录
     *
     * @param userId 用户ID
     * @param isConverted 是否已转化
     * @return 广告跟踪记录列表
     */
    default List<MemberAdTrackingDO> selectListByUserIdAndConverted(Long userId, Boolean isConverted) {
        return selectList(new LambdaQueryWrapperX<MemberAdTrackingDO>()
                .eq(MemberAdTrackingDO::getUserId, userId)
                .eq(MemberAdTrackingDO::getIsConverted, isConverted));
    }

    /**
     * 分页查询广告跟踪记录
     *
     * @param pageParam 分页参数
     * @param userId 用户ID
     * @param utmSource 广告来源
     * @param utmCampaign 广告活动
     * @param isConverted 是否已转化
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    default PageResult<MemberAdTrackingDO> selectPage(PageParam pageParam, Long userId, String utmSource, 
                                                      String utmCampaign, Boolean isConverted, 
                                                      LocalDateTime beginTime, LocalDateTime endTime) {
        return selectPage(pageParam, new LambdaQueryWrapperX<MemberAdTrackingDO>()
                .eqIfPresent(MemberAdTrackingDO::getUserId, userId)
                .likeIfPresent(MemberAdTrackingDO::getUtmSource, utmSource)
                .likeIfPresent(MemberAdTrackingDO::getUtmCampaign, utmCampaign)
                .eqIfPresent(MemberAdTrackingDO::getIsConverted, isConverted)
                .betweenIfPresent(MemberAdTrackingDO::getCreateTime, beginTime, endTime)
                .orderByDesc(MemberAdTrackingDO::getId));
    }

}
