-- 会员广告跟踪表
CREATE TABLE member_ad_tracking (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(64) COMMENT '会话ID',
    
    -- UTM参数
    utm_source VARCHAR(50) COMMENT '广告来源',
    utm_medium VARCHAR(50) COMMENT '广告媒介',
    utm_campaign VARCHAR(100) COMMENT '广告活动',
    utm_term VARCHAR(100) COMMENT '广告关键词',
    utm_content VARCHAR(100) COMMENT '广告内容',
    
    -- 平台特定参数
    gclid VARCHAR(100) COMMENT 'Google点击ID',
    fbclid VARCHAR(100) COMMENT 'Facebook点击ID',
    
    -- 跟踪信息
    landing_page VARCHAR(500) COMMENT '着陆页面',
    referrer VARCHAR(500) COMMENT '来源页面',
    user_agent VARCHAR(500) COMMENT '用户代理',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    
    -- 转化信息
    is_converted TINYINT(1) DEFAULT 0 COMMENT '是否已转化',
    conversion_type VARCHAR(20) COMMENT '转化类型：REGISTER,ORDER,CUSTOM',
    conversion_value DECIMAL(10,2) COMMENT '转化价值',
    conversion_time DATETIME COMMENT '转化时间',
    
    -- 系统字段
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    tenant_id BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
    
    INDEX idx_user_id (user_id),
    INDEX idx_utm_source (utm_source),
    INDEX idx_session_id (session_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员广告跟踪表';
